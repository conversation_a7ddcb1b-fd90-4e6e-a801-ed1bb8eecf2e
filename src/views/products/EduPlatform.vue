<!-- TunShuEdu AI智教平台页面 -->
<template>
  <div class="min-h-screen bg-gray-50">
    <PageHeader>
      TunShuEdu AI智教平台
      <template #subtitle>
        运用尖端大模型AI，赋能教育咨询机构，实现服务智能化升级与效率革新。
      </template>
    </PageHeader>

    <div class="py-24 sm:py-32">
      <div class="mx-auto max-w-7xl px-6 lg:px-8">
        <!-- 痛点聚焦与引言 -->
        <div class="text-center mb-32 opacity-0 fade-in-up">
          <div class="relative">
            <!-- 装饰性背景 -->
            <div class="absolute inset-0 overflow-hidden">
              <div class="absolute top-10 left-1/4 w-24 h-24 bg-red-500/10 rounded-full blur-xl"></div>
              <div class="absolute bottom-10 right-1/4 w-32 h-32 bg-orange-500/10 rounded-full blur-xl"></div>
            </div>

            <div class="relative bg-white rounded-3xl p-12 shadow-lg border border-gray-100 max-w-4xl mx-auto">
              <h2 class="text-3xl font-bold tracking-tight text-gray-900 sm:text-4xl mb-8">
                {{ painPoints.title }}
              </h2>
              <p class="text-lg text-gray-600 leading-relaxed mb-6">
                {{ painPoints.challenges }}
              </p>
              <div class="w-16 h-1 bg-gradient-to-r from-primary-500 to-blue-500 rounded-full mx-auto mb-6"></div>
              <p class="text-xl font-semibold text-primary-600 leading-relaxed">
                {{ painPoints.solution }}
              </p>
            </div>
          </div>
        </div>

        <!-- 产品核心优势 -->
        <div class="mb-32">
          <div class="text-center mb-16 opacity-0 fade-in-up">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">
              为何选择TunShuEdu？三大核心优势为您保驾护航
            </h2>
          </div>

          <div class="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div v-for="(advantage, index) in coreAdvantages" :key="advantage.title"
                 class="group relative bg-white rounded-2xl p-8 hover-float opacity-0 fade-in-up border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl"
                 :class="`delay-${(index + 1) * 200}`">
              <!-- 图标区域 -->
              <div class="flex items-center justify-center w-16 h-16 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl mb-6 group-hover:from-primary-200 group-hover:to-primary-300 transition-all duration-300">
                <component :is="advantage.icon" class="h-8 w-8 text-primary-600 group-hover:scale-110 transition-transform duration-300" aria-hidden="true" />
              </div>

              <!-- 内容区域 -->
              <div class="space-y-4">
                <h3 class="text-xl font-semibold text-gray-900 group-hover:text-primary-600 transition-colors duration-300">
                  {{ advantage.title }}
                </h3>
                <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                  {{ advantage.description }}
                </p>
              </div>

              <!-- 悬浮时的装饰线 -->
              <div class="absolute bottom-0 left-8 right-8 h-px bg-gradient-to-r from-transparent via-primary-200 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300"></div>
            </div>
          </div>
        </div>

        <!-- 平台核心功能 -->
        <div class="mb-32">
          <div class="text-center mb-16 opacity-0 fade-in-up">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">
              释放潜能：TunShuEdu核心功能详解
            </h2>
          </div>

          <!-- 使用交替布局避免审美疲劳 -->
          <div class="space-y-16">
            <div v-for="(feature, index) in coreFeatures" :key="feature.name"
                 class="opacity-0 fade-in-up"
                 :class="`delay-${(index + 1) * 200}`">

              <!-- 奇数项：左图标右内容 -->
              <div v-if="index % 2 === 0" class="flex flex-col lg:flex-row items-center gap-12">
                <div class="flex-shrink-0">
                  <div class="relative">
                    <!-- 装饰性背景 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-primary-500/20 to-blue-500/20 rounded-full blur-xl"></div>
                    <div class="relative flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-primary-100 to-primary-200 shadow-lg">
                      <component :is="feature.icon" class="h-12 w-12 text-primary-600" aria-hidden="true" />
                    </div>
                  </div>
                </div>
                <div class="flex-1 text-center lg:text-left">
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ feature.name }}</h3>
                  <p class="text-lg text-gray-600 leading-relaxed">{{ feature.description }}</p>
                </div>
              </div>

              <!-- 偶数项：右图标左内容 -->
              <div v-else class="flex flex-col lg:flex-row-reverse items-center gap-12">
                <div class="flex-shrink-0">
                  <div class="relative">
                    <!-- 装饰性背景 -->
                    <div class="absolute inset-0 bg-gradient-to-br from-blue-500/20 to-purple-500/20 rounded-full blur-xl"></div>
                    <div class="relative flex h-24 w-24 items-center justify-center rounded-2xl bg-gradient-to-br from-blue-100 to-purple-100 shadow-lg">
                      <component :is="feature.icon" class="h-12 w-12 text-blue-600" aria-hidden="true" />
                    </div>
                  </div>
                </div>
                <div class="flex-1 text-center lg:text-right">
                  <h3 class="text-2xl font-bold text-gray-900 mb-4">{{ feature.name }}</h3>
                  <p class="text-lg text-gray-600 leading-relaxed">{{ feature.description }}</p>
                </div>
              </div>

              <!-- 分隔线 -->
              <div v-if="index < coreFeatures.length - 1" class="flex justify-center mt-16">
                <div class="w-32 h-px bg-gradient-to-r from-transparent via-gray-300 to-transparent"></div>
              </div>
            </div>
          </div>
        </div>

        <!-- 典型应用场景 -->
        <div class="mb-32">
          <div class="text-center mb-16 opacity-0 fade-in-up">
            <h2 class="text-3xl font-bold tracking-tight text-primary-600 sm:text-4xl">
              赋能多元教育场景，提升核心竞争力
            </h2>
            <p class="mt-6 text-xl text-gray-600 max-w-4xl mx-auto leading-relaxed">
              无论是留学规划、语言培训，还是国际学校运营、在线教育创新，TunShuEdu都能提供精准适配的智能化支持。
            </p>
          </div>

          <!-- 使用网格布局，但每个卡片有不同的设计 -->
          <div class="grid grid-cols-1 lg:grid-cols-2 gap-8">
            <div v-for="(scenario, index) in applicationScenarios" :key="scenario.title"
                 class="group relative overflow-hidden rounded-2xl bg-white border border-gray-100 hover:border-gray-200 transition-all duration-300 hover:shadow-xl opacity-0 fade-in-up"
                 :class="[
                   `delay-${(index + 1) * 200}`,
                   index === 0 ? 'lg:col-span-2' : '',
                   index === 1 ? 'bg-gradient-to-br from-blue-50 to-indigo-50' : '',
                   index === 2 ? 'bg-gradient-to-br from-purple-50 to-pink-50' : '',
                   index === 3 ? 'bg-gradient-to-br from-green-50 to-emerald-50' : ''
                 ]">

              <!-- 第一个场景：特殊大卡片布局 -->
              <div v-if="index === 0" class="flex flex-col lg:flex-row">
                <div class="flex-1 p-8">
                  <div class="flex items-center gap-4 mb-6">
                    <div class="flex items-center justify-center w-12 h-12 bg-gradient-to-br from-primary-100 to-primary-200 rounded-xl">
                      <component :is="scenario.icon" class="h-6 w-6 text-primary-600" aria-hidden="true" />
                    </div>
                    <h3 class="text-2xl font-bold text-gray-900">{{ scenario.title }}</h3>
                  </div>
                  <p class="text-lg text-gray-600 leading-relaxed">{{ scenario.description }}</p>
                </div>
                <div class="lg:w-1/3 p-8 flex items-center justify-center">
                  <div class="w-32 h-32 bg-gradient-to-br from-primary-500/20 to-blue-500/20 rounded-full flex items-center justify-center">
                    <component :is="scenario.icon" class="h-16 w-16 text-primary-600" aria-hidden="true" />
                  </div>
                </div>
              </div>

              <!-- 其他场景：标准卡片布局 -->
              <div v-else class="p-8">
                <div class="flex items-center gap-4 mb-6">
                  <div class="flex items-center justify-center w-12 h-12 rounded-xl"
                       :class="{
                         'bg-gradient-to-br from-blue-100 to-indigo-200': index === 1,
                         'bg-gradient-to-br from-purple-100 to-pink-200': index === 2,
                         'bg-gradient-to-br from-green-100 to-emerald-200': index === 3
                       }">
                    <component :is="scenario.icon" class="h-6 w-6"
                               :class="{
                                 'text-blue-600': index === 1,
                                 'text-purple-600': index === 2,
                                 'text-green-600': index === 3
                               }" aria-hidden="true" />
                  </div>
                  <h3 class="text-xl font-bold text-gray-900 group-hover:text-gray-700 transition-colors duration-300">
                    {{ scenario.title }}
                  </h3>
                </div>
                <p class="text-gray-600 leading-relaxed group-hover:text-gray-700 transition-colors duration-300">
                  {{ scenario.description }}
                </p>

                <!-- 装饰性元素 -->
                <div class="absolute top-4 right-4 w-2 h-2 rounded-full opacity-0 group-hover:opacity-100 transition-opacity duration-300"
                     :class="{
                       'bg-blue-400': index === 1,
                       'bg-purple-400': index === 2,
                       'bg-green-400': index === 3
                     }"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import PageHeader from '@/components/PageHeader.vue'
import {
  PAIN_POINTS,
  CORE_ADVANTAGES,
  CORE_FEATURES,
  APPLICATION_SCENARIOS
} from '@/constants/eduPlatform'

// 使用导入的常量数据
const painPoints = PAIN_POINTS
const coreAdvantages = CORE_ADVANTAGES
const coreFeatures = CORE_FEATURES
const applicationScenarios = APPLICATION_SCENARIOS
</script>
